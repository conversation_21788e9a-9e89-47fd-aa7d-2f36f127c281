<template>
  <div>
    <el-dialog
      :title="isEditMode ? $T('编辑资源') : $T('新增资源')"
      :visible.sync="dialogVisible"
      width="960px"
      :destroy-on-close="true"
    >
      <div class="dialog-content">
        <el-form
          ref="addResourceForm"
          :model="formData"
          :rules="formRules"
          label-width="100px"
          label-position="top"
          style="padding: 20px 0"
        >
          <!-- 第一行：电户号、资源名称、报装容量 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item prop="electricityUserNumbers" :label="$T('电户号')">
                <el-input
                  v-model="formData.electricityUserNumbers"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="resourceName" :label="$T('资源名称')">
                <el-input
                  v-model="formData.resourceName"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                prop="registeredCapacity"
                :label="$T('技术容量') + ' (kVA)'"
              >
                <el-input
                  v-model="formData.registeredCapacity"
                  :placeholder="$T('请输入数值')"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第二行：平台直控、资源类型、响应方式 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                prop="platformDirectControl"
                :label="$T('平台直控')"
              >
                <el-select
                  v-model="formData.platformDirectControl"
                  :placeholder="$T('请选择')"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in platformDirectControlOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="resourceType" :label="$T('资源类型')">
                <el-select
                  v-model="formData.resourceType"
                  :placeholder="$T('请选择')"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in resourceTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="responseMode" :label="$T('响应方式')">
                <el-select
                  v-model="formData.responseMode"
                  :placeholder="$T('请选择')"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in responseModeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第三行：资源状态、最大可运行功率、最小可运行功率 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item prop="resourceStatus" :label="$T('资源状态')">
                <el-select
                  v-model="formData.resourceStatus"
                  :placeholder="$T('请选择')"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in resourceStatusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                prop="maxRunningPower"
                :label="$T('最大可运行功率') + ' (kW)'"
              >
                <el-input
                  v-model="formData.maxRunningPower"
                  :placeholder="$T('请输入数值')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                prop="minRunningPower"
                :label="$T('最小可运行功率') + ' (kW)'"
              >
                <el-input
                  v-model="formData.minRunningPower"
                  :placeholder="$T('请输入数值')"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 第四行：最大上调速率、最大下调速率、经度 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                prop="maxUpRate"
                :label="$T('最大上调速率') + ' (kW/min)'"
              >
                <el-input
                  v-model="formData.maxUpRate"
                  :placeholder="$T('请输入数值')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                prop="maxDownRate"
                :label="$T('最大下调速率') + ' (kW/min)'"
              >
                <el-input
                  v-model="formData.maxDownRate"
                  :placeholder="$T('请输入数值')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="longitude" :label="$T('经度')">
                <el-input
                  v-model="formData.longitude"
                  :placeholder="$T('请选择')"
                  clearable
                >
                  <template #append>
                    <el-button
                      type="primary"
                      icon="el-icon-paperclip"
                      @click="handleSelectMap"
                    ></el-button>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第五行：纬度、联系人、联系电话 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item prop="latitude" :label="$T('纬度')">
                <el-input
                  v-model="formData.latitude"
                  :placeholder="$T('请选择')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="contactPerson" :label="$T('联系人')">
                <el-input
                  v-model="formData.contactPerson"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="contactPhone" :label="$T('联系电话')">
                <el-input
                  v-model="formData.contactPhone"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第六行：区域 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item prop="region" :label="$T('区域')">
                <el-cascader
                  v-model="formData.region"
                  :options="regionOptions"
                  :placeholder="$T('请选择区域')"
                  size="small"
                  style="width: 100%"
                  clearable
                  :props="{ value: 'code', label: 'name' }"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第七行：地址 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item prop="address" :label="$T('地址')">
                <el-input
                  v-model="formData.address"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">{{ $T("取消") }}</el-button>
        <el-button type="primary" @click="handleSave">
          {{ isEditMode ? $T("保存") : $T("确定") }}
        </el-button>
      </div>
    </el-dialog>
    <CetSelectMapPointDialog
      class="map-dialog"
      v-model="showSelectMap"
      :initialCoordinates="initialCoordinates"
      :theme="mapTheme"
      @confirm="handlePointSelect"
    />
  </div>
</template>

<script>
import { getEnumOptions } from "../../../../utils/enumManager";
import { getGeographicalData } from "@/api/base-config";

import OmegaTheme from "@omega/theme";

export default {
  name: "ResourceDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: "add", // 'add' 或 'edit'
      validator: value => ["add", "edit"].includes(value)
    },
    resourceData: {
      type: Object,
      default: () => ({})
    },
    province: {
      type: Number,
      default: null
    },
    city: {
      type: Number,
      default: null
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.handleClose();
        }
      }
    },
    // 是否为编辑模式
    isEditMode() {
      return this.mode === "edit";
    },
    // 从 enumManager 获取资源类型选项
    resourceTypeOptions() {
      return getEnumOptions("RESOURCE_TYPE");
    },
    // 从 enumManager 获取响应方式选项
    responseModeOptions() {
      return getEnumOptions("RESPONSE_MODE");
    },
    // 从 enumManager 获取资源状态选项
    resourceStatusOptions() {
      return getEnumOptions("RESOURCE_STATUS_CODES");
    }
  },
  data() {
    return {
      formData: {
        electricityUserNumbers: "",
        resourceName: "",
        registeredCapacity: "",
        platformDirectControl: "",
        resourceType: "",
        responseMode: "",
        resourceStatus: "",
        maxRunningPower: "",
        minRunningPower: "",
        maxUpRate: "",
        maxDownRate: "",
        longitude: "",
        latitude: "",
        contactPerson: "",
        contactPhone: "",
        region: [],
        address: ""
      },
      formRules: {
        electricityUserNumbers: [
          {
            required: true,
            message: this.$T("电户号不能为空"),
            trigger: "blur"
          }
        ],
        resourceName: [
          {
            required: true,
            message: this.$T("资源名称不能为空"),
            trigger: "blur"
          }
        ],
        registeredCapacity: [
          {
            required: true,
            message: this.$T("报装容量不能为空"),
            trigger: "blur"
          }
        ],
        platformDirectControl: [
          {
            required: true,
            message: this.$T("请选择平台直控"),
            trigger: "change"
          }
        ],
        resourceType: [
          {
            required: true,
            message: this.$T("请选择资源类型"),
            trigger: "change"
          }
        ],
        responseMode: [
          {
            required: true,
            message: this.$T("请选择响应方式"),
            trigger: "change"
          }
        ],
        resourceStatus: [
          {
            required: true,
            message: this.$T("请选择资源状态"),
            trigger: "change"
          }
        ],
        maxRunningPower: [
          {
            required: true,
            message: this.$T("最大可运行功率不能为空"),
            trigger: "blur"
          }
        ],
        minRunningPower: [
          {
            required: true,
            message: this.$T("最小可运行功率不能为空"),
            trigger: "blur"
          }
        ],
        region: [
          {
            required: true,
            message: this.$T("请选择区域"),
            trigger: "change"
          }
        ]
      },
      // 平台直控选项
      platformDirectControlOptions: [
        { label: "是", value: true },
        { label: "否", value: false }
      ],

      // 区域选项
      regionOptions: [],
      showSelectMap: false,
      initialCoordinates: null,
      mapTheme: "light"
    };
  },
  watch: {
    // 监听省份变化，重新加载地理数据
    province: {
      handler() {
        this.loadGeographicalData();
      }
    },
    // 监听城市变化，重新加载地理数据
    city: {
      handler() {
        this.loadGeographicalData();
      }
    },
    // 监听资源数据变化，编辑模式下预填充数据
    resourceData: {
      handler(newData) {
        if (this.isEditMode && newData && Object.keys(newData).length > 0) {
          this.fillFormData(newData);
        }
      },
      immediate: true,
      deep: true
    },
    // 监听弹窗显示状态
    visible(newVal) {
      if (newVal) {
        if (
          this.isEditMode &&
          this.resourceData &&
          Object.keys(this.resourceData).length > 0
        ) {
          this.fillFormData(this.resourceData);
        } else if (!this.isEditMode) {
          this.resetForm();
        }
      }
    }
  },
  mounted() {
    this.loadGeographicalData();
  },
  methods: {
    // 加载地理数据
    async loadGeographicalData() {
      try {
        const response = await getGeographicalData();
        if (response.code === 0) {
          this.regionOptions = this.transformGeographicalData(
            response.data,
            this.province,
            this.city
          );
        } else {
          this.$message.error(this.$T("加载地理数据失败"));
        }
      } catch (error) {
        console.error("加载地理数据失败:", error);
        this.$message.error(this.$T("加载地理数据失败"));
      }
    },

    // 转换地理数据为级联选择器格式
    transformGeographicalData(data, provinceId, cityId) {
      if (!data) return [];
      let filteredData = data;
      if (provinceId) {
        filteredData = filteredData.filter(p => p.code === provinceId);
      }
      if (cityId) {
        filteredData.forEach(p => {
          if (p.children) {
            p.children = p.children.filter(c => c.code === cityId);
          }
        });
      }
      return filteredData;
    },
    handleClose() {
      // 重置表单
      this.resetForm();
      this.$emit("close");
    },

    // 重置表单数据
    resetForm() {
      // 使用Element UI的resetFields方法
      this.$refs.addResourceForm?.resetFields();

      // 手动重置表单数据，确保完全清空
      this.formData = {
        electricityUserNumbers: "",
        resourceName: "",
        registeredCapacity: "",
        platformDirectControl: "",
        resourceType: "",
        responseMode: "",
        resourceStatus: "",
        maxRunningPower: "",
        minRunningPower: "",
        maxUpRate: "",
        maxDownRate: "",
        longitude: "",
        latitude: "",
        contactPerson: "",
        contactPhone: "",
        region: [],
        address: ""
      };
    },

    // 填充表单数据（编辑模式）
    fillFormData(data) {
      if (!data) return;

      console.log("🔍 编辑模式 - 原始数据:", data);

      this.formData = {
        electricityUserNumbers: this.getFieldValue(data, [
          "electricityUserNumbers"
        ]),
        resourceName: this.getFieldValue(data, ["resourceName"]),
        registeredCapacity: this.getFieldValue(data, ["registeredCapacity"]),
        platformDirectControl: this.getFieldValue(data, [
          "platformDirectControl"
        ]),
        resourceType: this.getFieldValue(data, ["resourceType"]),
        responseMode: this.getFieldValue(data, ["responseMode"]),
        resourceStatus: this.getFieldValue(data, ["resourceStatus"]),
        maxRunningPower: this.getFieldValue(data, ["maximumOperablePower"]),
        minRunningPower: this.getFieldValue(data, ["minimumOperablePower"]),
        maxUpRate: this.getFieldValue(data, ["maximumUpscalingRate"]),
        maxDownRate: this.getFieldValue(data, ["maximumDownwardRate"]),
        longitude: this.getFieldValue(data, ["longitude"]),
        latitude: this.getFieldValue(data, ["latitude"]),
        contactPerson: this.getFieldValue(data, ["contactPerson"]),
        contactPhone: this.getFieldValue(data, ["phoneNumber"]),
        region: this.buildRegionArray(data.province, data.city, data.district),
        address: this.getFieldValue(data, ["address"])
      };

      console.log("🔍 编辑模式 - 填充后的表单数据:", this.formData);
    },

    // 获取字段值的辅助方法
    getFieldValue(data, fieldNames) {
      for (const fieldName of fieldNames) {
        if (data[fieldName] !== undefined && data[fieldName] !== null) {
          const value = data[fieldName];
          // 特殊处理布尔值（如平台直控字段）
          if (typeof value === "boolean") {
            return value; // 直接返回布尔值，与选择器选项匹配
          }
          // 如果是数字类型，保持数字类型以便与选择器选项匹配
          if (typeof value === "number") {
            return value;
          }
          // 其他类型转换为字符串
          return String(value);
        }
      }
      return "";
    },

    // 构建区域数组
    buildRegionArray(province, city, district) {
      const region = [];
      if (province) region.push(province);
      if (city) region.push(city);
      if (district) region.push(district);
      return region;
    },

    async handleSave() {
      try {
        // 表单验证
        await this.$refs.addResourceForm.validate();

        // 提交数据
        const [provinceId, cityId, districtId] = this.formData.region || [];
        const formData = {
          ...this.formData,
          province: provinceId,
          city: cityId,
          district: districtId
        };

        // 移除临时的 region 字段，避免传递给后端
        delete formData.region;

        // 根据模式发送不同的事件
        if (this.isEditMode) {
          // 编辑模式：添加资源ID
          formData.id = this.resourceData.id;
          this.$emit("save", formData);
        } else {
          // 新增模式
          this.$emit("save", formData);
        }

        // 注意：成功消息和关闭弹窗的操作现在由父组件处理
        // 这样可以确保只有在API调用成功后才关闭弹窗和显示成功消息
      } catch (error) {
        console.error("表单验证失败:", error);
        this.$message.error(this.$T("请检查表单输入"));
      }
    },
    handleSelectMap() {
      this.showSelectMap = true;
    },
    handlePointSelect(coordinates) {
      console.log(coordinates);

      if (!this._.isEmpty(coordinates)) {
        this.initialCoordinates = this._.cloneDeep(coordinates);
        this.CetForm_pagedialog.data.longitude = coordinates[0];
        this.CetForm_pagedialog.data.latitude = coordinates[1];
        this.location = coordinates.join(",");
        const decoder = new BMap.Geocoder();
        const point = new BMap.Point(...coordinates);
        console.log(point);
      } else {
        this.$message.warning("未选择任何点");
      }
    }
  }
};
</script>

<style scoped></style>
